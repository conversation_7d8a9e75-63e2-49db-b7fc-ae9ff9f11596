package domain

import (
	"mime/multipart"
	"time"
)

// Employee struct
type Employee struct {
	NO              int     `json:"NO"`
	HrmEmployeeID   int     `json:"HEMP_ID"`
	Nik             string  `json:"NIK"`
	HrEmployeeID    int     `json:"ID"`
	EmployeeSallary int     `json:"SALLARY"`
	EmployeeFkid    int     `json:"HEMP_FKID"`
	TypeFkid        int     `json:"TYPE_FKID"`
	MaxLeave        int     `json:"MAX_LEAVE"`
	Name            string  `json:"NAME"`
	EmployeeID      int     `json:"EMP_ID"`
	TypeName        string  `json:"TYPE_NAME"`
	AdminFkid       int     `json:"admin_fkid"`
	DataCreated     *string `json:"data_created"`
	DataModified    *string `json:"data_modified"`
	DataStatus      string  `json:"data_status"`
	DateJoin        int64   `json:"date_join"`
	JabatanFkid     int     `json:"jabatan_fkid"`
	JabatanName     string  `json:"jabatan_name"`
}

// SingleEmployee single employee
type SingleEmployee struct {
	HrmEmployeeID  int    `json:"hrm_employee_id"`
	AdminFkid      int    `json:"admin_fkid"`
	DataCreated    int    `json:"data_created"`
	DataModified   int    `json:"data_modified"`
	EmployeeFkid   int    `json:"employee_fkid"`
	DataStatus     string `json:"data_status"`
	Nik            string `json:"nik"`
	TypeFkid       int    `json:"type_fkid"`
	MaxLeave       int    `json:"max_leave"`
	EmployeeSalary int    `json:"employee_sallary"`
	ProfileImg     string `json:"profile_img"`
}

// HrmAddInfo hrm add info
type HrmAddInfo struct {
	AddID           int    `json:"add_id"`
	HrmEmployeeFkid int    `json:"hrm_employee_fkid"`
	Title           string `json:"title"`
	Info            string `json:"info"`
	Attachment      string `json:"attachment"`
}

type EmployeeAttach struct {
	Attachment string `json:"attachment"`
}

type VectorImg struct {
	ImgVector string `json:"img_vector"`
}

type ProfileImage struct {
	ProfileImg string `json:"profile_img"`
}

type EmailNPassword struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// EmployeeData struct for employee creation/update
type EmployeeData struct {
	HrmEmployeeID          string `json:"hrm_employee_id" validate:"string"`
	EmployeeFkid           string `json:"employee_fkid" validate:"string"`
	Nik                    string `json:"nik" validate:"required" message:"required:Nik harus di isi"`
	EmployeeSallary        int    `json:"employee_sallary" validate:"required|int" message:"required:Employee sallary harus di isi|int:Employee sallary harus berupa angka"`
	TypeFkid               string `json:"type_fkid" validate:"required" message:"required:Tipe harus di isi"`
	MaxLeave               string `json:"max_leave" validate:"required" message:"required:Maksimum cuti harus di isi"`
	Name                   string `json:"name" validate:"required" message:"required:Nama harus di isi"`
	Address                string `json:"address" validate:"required" message:"required:Alamat harus di isi"`
	Phone                  string `json:"phone" validate:"required" message:"required:Nomor telepon harus di isi"`
	JabatanId              string `json:"jabatan_id" validate:"required" message:"required:Jabatan harus di isi"`
	JoinDate               string `json:"join_date" validate:"required|date" message:"required:Tanggal bergabung harus di isi|date:Format tanggal tidak valid"`
	Email                  string `json:"email" validate:"required|email" message:"required:Email harus di isi|email:Format email tidak valid"`
	Npwp                   string `json:"npwp"`       // Not required
	OutletIds              string `json:"outlet_ids"` // Comma-separated outlet IDs
	DataCreated            int    `json:"data_created" validate:"int"`
	DataModified           int    `json:"data_modified" validate:"int"`
	AdminId                int    `json:"admin_id"`
	UpdateInfoData         []map[string]any
	UpdateInfoDataWithAttc []map[string]any
	AddInfoData            []map[string]any
}

// EmployeeDetail struct for detailed employee information from both tables
type EmployeeDetail struct {
	// hrm_employee fields
	HrmEmployeeID   int    `json:"hrm_employee_id"`
	AdminFkid       int    `json:"admin_fkid"`
	EmployeeFkid    int    `json:"employee_fkid"`
	ImgVector       string `json:"img_vector"`
	ProfileImg      string `json:"profile_img"`
	DataStatus      string `json:"data_status"`
	Nik             string `json:"nik"`
	TypeFkid        int    `json:"type_fkid"`
	MaxLeave        int    `json:"max_leave"`
	EmployeeSallary int    `json:"employee_sallary"`
	HrmDataCreated  int64  `json:"hrm_data_created"`
	JointDate       string `json:"joint_date"`
	HrmDataModified int64  `json:"hrm_data_modified"`
	Npwp            string `json:"npwp"`

	// employee fields
	EmployeeID      int    `json:"employee_id"`
	Name            string `json:"name"`
	Address         string `json:"address"`
	Phone           string `json:"phone"`
	Photo           string `json:"photo"`
	LastLogin       int64  `json:"last_login"`
	JabatanFkid     int    `json:"jabatan_fkid"`
	Level           int    `json:"level"`
	Email           string `json:"email"`
	Password        string `json:"password,omitempty"`
	Pin             string `json:"pin,omitempty"`
	Role            string `json:"role"`
	RoleMobile      string `json:"role_mobile"`
	AccessMode      string `json:"access_mode"`
	DateJoin        int64  `json:"date_join"`
	EmpDataCreated  int64  `json:"emp_data_created"`
	EmpDataModified int64  `json:"emp_data_modified"`
	EmpDataStatus   string `json:"emp_data_status"`

	//join table
	EmployeesJabatanName string `json:"employees_jabatan_name"`
	HrmEmployeeTypeName  string `json:"hrm_employee_type_name"`

	Outlets []EmployeeOutlet `json:"outlets"`
}

func (e EmployeeData) ToEmployeeMap(timeDiff int64) map[string]any {
	//e.JoinDate = "2023-01-01" convert to timeMillis
	jointDateMillis := int64(0)

	if e.JoinDate != "" {
		// Parse the join date string to time.Time
		layout := "2006-01-02" // Format for YYYY-MM-DD
		joinTime, err := time.Parse(layout, e.JoinDate)
		if err == nil {
			// Convert to Unix timestamp (seconds)
			unixTime := joinTime.Unix()
			// Add the time difference (in seconds)
			adjustedTime := unixTime + timeDiff
			// Convert to milliseconds
			jointDateMillis = adjustedTime * 1000
		}
	}

	return map[string]any{
		"name":          e.Name,
		"address":       e.Address,
		"phone":         e.Phone,
		"email":         e.Email,
		"jabatan_fkid":  e.JabatanId,
		"date_join":     jointDateMillis,
		"data_created":  e.DataCreated,
		"data_modified": time.Now().UnixMilli(),
	}
}

// ToHrmEmployeeMap converts EmployeeData to a map for hrm_employee table
func (e EmployeeData) ToHrmEmployeeMap() map[string]any {
	return map[string]any{
		"nik":              e.Nik,
		"type_fkid":        e.TypeFkid,
		"max_leave":        e.MaxLeave,
		"employee_sallary": e.EmployeeSallary,
		"npwp":             e.Npwp,
		"joint_date":       e.JoinDate, // Store as YYYY-MM-DD format
		"data_created":     e.DataCreated,
		"data_modified":    e.DataModified,
		"data_status":      "on",
	}
}

// EmployeeContract interface
type EmployeeContract interface {
	Fetch(adminFkid int) ([]Employee, error)
	FetchSingle(hrmID int, employeeID int) (EmployeeDetail, error) // Updated to use EmployeeDetail and support both IDs
	AddEmployee(employee EmployeeData) (int64, error)
	GetAddInfo(empID int) ([]HrmAddInfo, error)
	AddInfo([]map[string]interface{}) error
	UpdateInfo([]map[string]interface{}) error
	DeleteAttach(where map[string]interface{}) error
	FetchEmpAttach(addID int) ([]EmployeeAttach, error)
	FetchEmpImg(empID int) (ProfileImage, error)
	GetImageVector(file1 multipart.File, file2 multipart.File, fileName string, empID string) error
	// v2 mobile api
	FetchVectorImg(empID int) ([]VectorImg, error)
	UpdatePofilePhoto(empID int, img string, vector interface{}) error
	ChangePassword(email string, nePass string, oldPass string) error
	FetchEmailNPassword(email string) (EmailNPassword, error)
	InsertUserKey(email string, BcryptStr string) error
}

// EmployeeFilter struct for filtering employees
type EmployeeFilter struct {
	OutletIDs       []int `json:"outlet_ids"`
	EmployeeTypeIDs []int `json:"employee_type_ids"`
	JabatanIDs      []int `json:"jabatan_ids"`
}

// EmployeeUseCase interface
type EmployeeUseCase interface {
	EmployeeContract
	AddEmployeeV1(employee EmployeeData) (int64, error)
	GetEmployeeV2(businessID int, filter ...EmployeeFilter) ([]map[string]interface{}, error)
	FetchAddInfo(hrmIDs int) ([]HrmAddInfo, error)
}

// EmployeeRepository interface
type EmployeeRepository interface {
	EmployeeContract
	FetchEmployeeOutlet(empID int) ([]EmployeeOutlet, error)
	FetchAddInfo(hrmIDs ...int) ([]HrmAddInfo, error)
	FetchWithFilter(adminFkid int, filter EmployeeFilter) ([]Employee, error)
}
