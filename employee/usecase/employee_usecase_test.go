package usecase

import (
	"mime/multipart"
	"testing"
	"time"

	"gitlab.com/backend/api-hrm/domain"
)

// Mock repository for testing
type mockEmployeeRepository struct{}

func (m *mockEmployeeRepository) Fetch(adminFkid int) ([]domain.Employee, error) {
	// Mock data with jabatan information
	now := time.Now()
	oneYearAgo := now.AddDate(-1, -2, -15)  // 1 year, 2 months, 15 days ago
	twoYearsAgo := now.AddDate(-2, -6, -10) // 2 years, 6 months, 10 days ago

	return []domain.Employee{
		{
			HrmEmployeeID:   1,
			EmployeeID:      101,
			Nik:             "1234567890123456",
			Name:            "<PERSON>",
			EmployeeSallary: 5000000,
			TypeName:        "Full Time",
			MaxLeave:        12,
			DateJoin:        oneYearAgo.UnixMilli(),
			JabatanFkid:     1,
			JabatanName:     "Software Engineer",
		},
		{
			HrmEmployeeID:   2,
			EmployeeID:      102,
			Nik:             "1234567890123457",
			Name:            "<PERSON>",
			EmployeeSallary: 6000000,
			TypeName:        "Full Time",
			MaxLeave:        12,
			DateJoin:        twoYearsAgo.UnixMilli(),
			JabatanFkid:     2,
			JabatanName:     "Senior Developer",
		},
	}, nil
}

func (m *mockEmployeeRepository) FetchWithFilter(adminFkid int, filter domain.EmployeeFilter) ([]domain.Employee, error) {
	return m.Fetch(adminFkid)
}

func (m *mockEmployeeRepository) FetchAddInfo(hrmIDs ...int) ([]domain.HrmAddInfo, error) {
	return []domain.HrmAddInfo{}, nil
}

// Implement other required methods with empty implementations for testing
func (m *mockEmployeeRepository) FetchSingle(hrmID int, employeeID int) (domain.EmployeeDetail, error) {
	return domain.EmployeeDetail{}, nil
}
func (m *mockEmployeeRepository) AddEmployee(employee domain.EmployeeData) (int64, error) {
	return 0, nil
}
func (m *mockEmployeeRepository) GetAddInfo(empID int) ([]domain.HrmAddInfo, error) {
	return []domain.HrmAddInfo{}, nil
}
func (m *mockEmployeeRepository) AddInfo([]map[string]interface{}) error          { return nil }
func (m *mockEmployeeRepository) UpdateInfo([]map[string]interface{}) error       { return nil }
func (m *mockEmployeeRepository) DeleteAttach(where map[string]interface{}) error { return nil }
func (m *mockEmployeeRepository) FetchEmpAttach(addID int) ([]domain.EmployeeAttach, error) {
	return []domain.EmployeeAttach{}, nil
}
func (m *mockEmployeeRepository) FetchEmpImg(empID int) (domain.ProfileImage, error) {
	return domain.ProfileImage{}, nil
}
func (m *mockEmployeeRepository) GetImageVector(file1, file2 multipart.File, fileName string, empID string) error {
	return nil
}
func (m *mockEmployeeRepository) FetchVectorImg(empID int) ([]domain.VectorImg, error) {
	return []domain.VectorImg{}, nil
}
func (m *mockEmployeeRepository) UpdatePofilePhoto(empID int, img string, vector interface{}) error {
	return nil
}
func (m *mockEmployeeRepository) ChangePassword(email string, nePass string, oldPass string) error {
	return nil
}
func (m *mockEmployeeRepository) FetchEmailNPassword(email string) (domain.EmailNPassword, error) {
	return domain.EmailNPassword{}, nil
}
func (m *mockEmployeeRepository) InsertUserKey(email string, BcryptStr string) error { return nil }
func (m *mockEmployeeRepository) FetchEmployeeOutlet(empID int) ([]domain.EmployeeOutlet, error) {
	return []domain.EmployeeOutlet{}, nil
}

func TestGetEmployeeV2_WithJabatanAndTenure(t *testing.T) {
	// Create usecase with mock repository
	mockRepo := &mockEmployeeRepository{}
	usecase := NewEmployeeUseCase(mockRepo)

	// Test GetEmployeeV2
	result, err := usecase.GetEmployeeV2(1)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Fatalf("Expected 2 employees, got %d", len(result))
	}

	// Check first employee
	emp1 := result[0]
	if emp1["name"] != "John Doe" {
		t.Errorf("Expected name 'John Doe', got %v", emp1["name"])
	}
	if emp1["position"] != "Software Engineer" {
		t.Errorf("Expected position 'Software Engineer', got %v", emp1["position"])
	}
	if emp1["jabatan_fkid"] != 1 {
		t.Errorf("Expected jabatan_fkid 1, got %v", emp1["jabatan_fkid"])
	}

	// Check tenure field exists
	if _, exists := emp1["tenure"]; !exists {
		t.Error("Expected tenure field to exist")
	}

	// Check second employee
	emp2 := result[1]
	if emp2["name"] != "Jane Smith" {
		t.Errorf("Expected name 'Jane Smith', got %v", emp2["name"])
	}
	if emp2["position"] != "Senior Developer" {
		t.Errorf("Expected position 'Senior Developer', got %v", emp2["position"])
	}
	if emp2["jabatan_fkid"] != 2 {
		t.Errorf("Expected jabatan_fkid 2, got %v", emp2["jabatan_fkid"])
	}

	// Verify tenure calculation makes sense (should contain "year" for both employees)
	if tenure1, ok := emp1["tenure"].(string); ok {
		if tenure1 == "" {
			t.Error("Expected tenure for emp1 to not be empty")
		}
		t.Logf("Employee 1 tenure: %s", tenure1)
	}

	if tenure2, ok := emp2["tenure"].(string); ok {
		if tenure2 == "" {
			t.Error("Expected tenure for emp2 to not be empty")
		}
		t.Logf("Employee 2 tenure: %s", tenure2)
	}
}
