package http

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"

	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/array"
	"gitlab.com/backend/api-hrm/core/util/bucket"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/core/util/request"
	"golang.org/x/crypto/bcrypt"

	"github.com/gofiber/fiber/v2"
	"github.com/gookit/validate"
	"gitlab.com/backend/api-hrm/domain"
)

// EmployeeHandler struct
type EmployeeHandler struct {
	EmployeeUseCase domain.EmployeeUseCase
}

// parseCommaSeparatedIDs parses a comma-separated string of IDs into a slice of integers
func parseCommaSeparatedIDs(param string) []int {
	var ids []int
	if param != "" {
		idStrs := strings.Split(param, ",")
		for _, idStr := range idStrs {
			if id, err := strconv.Atoi(strings.TrimSpace(idStr)); err == nil {
				ids = append(ids, id)
			}
		}
	}
	return ids
}

// NewEmployeeHandler func
func NewEmployeeHandler(app *fiber.App, uc domain.EmployeeUseCase) {
	handler := &EmployeeHandler{EmployeeUseCase: uc}
	v0 := app.Group("/v0")
	v0.Post("/add_employee", handler.AddEmployee)

	v1 := app.Group("/v1")
	v1.Get("/employees", handler.Fetch)
	v1.Get("/employee/:id", handler.FetchSingle)
	v1.Get("/get_info/:id", handler.GetAddInfo)
	v1.Post("/add_employee", handler.AddEmployeeV1)
	v1.Post("/delete_attach", handler.DeleteAttach)

	v2 := app.Group("/v2")
	v2.Get("/get_vector/:emp_id", handler.FetchVectorImg)
	v2.Post("/update_profile_photo", handler.UpdateProfilePhoto)
	v2.Post("/change_password", handler.ChangePassword)
	v2.Get("/employee", handler.GetEmployeeV2)
}

// Fetch all employee
// @Summary Get all employees
// @Description Get a list of all employees filtered by business ID from the header
// @Tags employee
// @Accept json
// @Produce json
// @Param business_id header string true "Business ID"
// @Success 200 {array} domain.Employee "List of employees"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/employees [get]
func (e *EmployeeHandler) Fetch(c *fiber.Ctx) error {

	// adminId := token.UserData.userId
	// adminId := c.Get("user_id")
	businessId := c.Get("business_id")
	fmt.Println(businessId)
	businessID, _ := strconv.Atoi(businessId)

	employees, err := e.EmployeeUseCase.Fetch(businessID)
	if err != nil {
		fmt.Println("error: ", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching employee",
			"error":   err,
		})
	}
	return c.JSON(employees)
}

// FetchSingle fetch single employee handler
// @Summary Get a single employee by ID
// @Description Get detailed information about a specific employee including additional info. Supports fetching by hrm_employee_id or employee_id. When hrm_employee_id is 0 or not available, use employee_id query parameter.
// @Tags employee
// @Accept json
// @Produce json
// @Param id path int true "HRM Employee ID (use 0 when fetching by employee_id)"
// @Param employee_id query int false "Employee ID (required when path id is 0)"
// @Success 200 {object} object "Employee details with additional info array"
// @Failure 400 {object} object "Bad request - missing required parameters"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/employee/{id} [get]
func (e *EmployeeHandler) FetchSingle(c *fiber.Ctx) error {
	// Get hrm_employee_id from path parameter
	hrmIDStr := c.Params("id")
	hrmID, _ := strconv.Atoi(hrmIDStr)

	// Get employee_id from query parameter
	employeeIDStr := c.Query("employee_id")
	employeeID, _ := strconv.Atoi(employeeIDStr)

	// Validate that at least one ID is provided
	if hrmID <= 0 && employeeID <= 0 {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "either hrm_employee_id (path parameter) or employee_id (query parameter) must be provided",
			"error":   "missing required parameters",
		})
	}

	employee, err := e.EmployeeUseCase.FetchSingle(hrmID, employeeID)
	if err != nil {
		fmt.Printf("error fetching single employee: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching single employee",
			"error":   err,
		})
	}
	info, err := e.EmployeeUseCase.FetchAddInfo(employee.HrmEmployeeID)
	if err != nil {
		fmt.Printf("error fetchin add info: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching add info",
			"error":   err,
		})
	}
	var infos []interface{}
	infos = append(infos, info)
	var res []interface{}
	res = append(res, employee, infos)
	return c.JSON(res)
}

// AddEmployee add employee handler
// @Summary Add or update an employee
// @Description Add a new employee or update an existing one with optional additional information and attachments
// @Tags employee
// @Accept multipart/form-data
// @Produce json
// @Param inputTypeId formData string false "Employee Type ID (empty for new employee)"
// @Param inputEmmployeeName formData string true "Employee Name"
// @Param inputNik formData string true "NIK (16 digits)"
// @Param inputSallary formData string true "Employee Salary"
// @Param inputEmployeeType formData string true "Employee Type"
// @Param inputCuti formData string true "Maximum Leave Days"
// @Param name formData string true "Full Name"
// @Param address formData string true "Address"
// @Param email formData string true "Email Address"
// @Param outlet_ids formData string false "Comma-separated list of outlet IDs that the employee has access to"
// @Param phone formData string true "Phone Number"
// @Param jabatanId formData string true "Position/Job Title ID"
// @Param joinDate formData string true "Join Date (YYYY-MM-DD)"
// @Param npwp formData string false "NPWP (Tax ID)"
// @Param inputTitle[] formData []string false "Additional Info Titles"
// @Param info_id[] formData []string false "Additional Info IDs (for updates)"
// @Param withFile[] formData []string false "Has Attachment Flags (true/false)"
// @Param inputInfo[] formData []string false "Additional Info Contents"
// @Param inputAttachment[] formData []file false "Attachments"
// @Param photo formData file false "Employee Photo"
// @Success 200 {object} object "Success response with status"
// @Failure 400 {object} object "Validation error"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/add_employee [post]
func (e *EmployeeHandler) AddEmployeeV1(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		log.Info("error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "wrong form key",
			"error":   err,
		})
	}

	// Define employee form struct
	type EmployeeForm struct {
		TypeID string `form:"inputTypeId"`
		// EmployeeFkid    string `form:"inputEmmployeeName"`
		EmployeeFkid    string `form:"inputEmployeeId"`
		Nik             string `form:"inputNik"`
		EmployeeSallary int    `form:"inputSallary"`
		TypeFkid        string `form:"inputEmployeeType"`
		MaxLeave        string `form:"inputCuti"`
		// New fields
		Name      string `form:"name"`
		Address   string `form:"address"`
		Phone     string `form:"phone"`
		Email     string `form:"email"`
		JabatanId string `form:"jabatanId"`
		JoinDate  string `form:"joinDate"`
		Npwp      string `form:"npwp"`       // Not required
		OutletIds string `form:"outlet_ids"` // Comma-separated outlet IDs
	}

	//print forms
	// fmt.Println(form.Value)
	log.Info("add employee: %v", cast.ToString(form.Value))

	// Parse employee form data
	employeeForm := new(EmployeeForm)
	if err := c.BodyParser(employeeForm); err != nil {
		fmt.Printf("error parsing employee form: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error parsing employee form",
			"error":   err,
		})
	}

	// Additional employe form
	titles := form.Value["inputTitle[]"]
	ids := form.Value["info_id[]"]
	withfiles := form.Value["withFile[]"]
	infos := form.Value["inputInfo[]"]
	files := form.File["inputAttachment[]"]
	log.Info("attachment files size: %v", len(files))

	// employee photo profile
	var photo []*multipart.FileHeader
	var profile_img []*multipart.FileHeader
	var fileName string
	if form.File["photo"] != nil {
		photo = form.File["photo"]
		profile_img = form.File["photo"]
		fileName = profile_img[0].Filename

		if photo[0] != nil {
			img_f, err := profile_img[0].Open()
			if err != nil {
				fmt.Printf("open file error: %v", err)
				return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
			}

			defer img_f.Close()
			f, err := photo[0].Open()
			if err != nil {
				fmt.Printf("opening file error: %v", err)
				return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
			}
			f.Close()
			go e.EmployeeUseCase.GetImageVector(f, img_f, fileName, employeeForm.EmployeeFkid)
		}
	}

	// Get url from bucket
	var attach []string
	if len(files) != 0 {
		for _, file := range files {
			//download the file to temporary dir
			savedPath, err := request.SaveToTempDir(file)
			if err != nil {
				log.Info("error saving to temp dir: %v", err)
				return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
			}
			attach = append(attach, savedPath)
		}
	}

	log.Info("attachemnts size: %v, list: %v", len(attach), attach)

	// Filtering files
	var group []string
	r := 0
	for _, k := range withfiles {
		if k == "true" {
			group = append(group, array.Get(attach, r))
			r = r + 1
		}
		if k == "false" {
			group = append(group, k)
		}
	}

	// Mapping additional form value
	var data []map[string]any
	if len(titles) == len(infos) {
		for i := range titles {
			data = append(data, map[string]any{
				"add_id":     array.Get(ids, i),
				"title":      array.Get(titles, i),
				"info":       array.Get(infos, i),
				"attachment": array.Get(group, i),
			})
		}
	}

	var updateInfoData []map[string]interface{}
	var updateInfoDataWithAttc []map[string]interface{}
	var addInfoData []map[string]interface{}
	// filtering info data for update and insert
	for _, v := range data {
		if v["add_id"] != "" {
			if v["attachment"] == "false" {
				delete(v, "attachment")
				updateInfoData = append(updateInfoData, v)
			} else if v["attachment"] != "false" {
				updateInfoDataWithAttc = append(updateInfoDataWithAttc, v)
			}
		}
		if v["add_id"] == "" {
			delete(v, "add_id")
			v["hrm_employee_fkid"] = employeeForm.TypeID
			addInfoData = append(addInfoData, v)
		}
	}

	user := domain.GetUserSessionFiber(c)
	// Create employee data struct
	employee := domain.EmployeeData{
		HrmEmployeeID:          employeeForm.TypeID,
		EmployeeFkid:           employeeForm.EmployeeFkid,
		Nik:                    employeeForm.Nik,
		EmployeeSallary:        employeeForm.EmployeeSallary,
		TypeFkid:               employeeForm.TypeFkid,
		MaxLeave:               employeeForm.MaxLeave,
		Name:                   employeeForm.Name,
		Address:                employeeForm.Address,
		Phone:                  employeeForm.Phone,
		Email:                  employeeForm.Email,
		JabatanId:              employeeForm.JabatanId,
		JoinDate:               employeeForm.JoinDate,
		Npwp:                   employeeForm.Npwp,
		OutletIds:              employeeForm.OutletIds,
		AdminId:                user.BusinessId,
		UpdateInfoData:         updateInfoData,
		UpdateInfoDataWithAttc: updateInfoDataWithAttc,
		AddInfoData:            addInfoData,
	}

	_, err = e.EmployeeUseCase.AddEmployeeV1(employee)
	log.Info("add employee success %v", err == nil)
	if err != nil {
		fmt.Printf("error adding employee: %v", err)
		if validationErr, ok := err.(*domain.ValidationException); ok {
			return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
				"message": validationErr.Message,
				"error":   validationErr.ValidatinFieldsErr,
			})
		}
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error adding employee",
			"error":   err,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "update data success",
		"status":  1,
	})
}

// AddEmployee add employee handler
// @Summary Add or update an employee (DEPRECATED)
// @Description Add a new employee or update an existing one with optional additional information and attachments
// @Tags employee
// @Accept multipart/form-data
// @Produce json
// @Param inputTypeId formData string false "Employee Type ID (empty for new employee)"
// @Param inputEmmployeeName formData string true "Employee Name"
// @Param inputNik formData string true "NIK (16 digits)"
// @Param inputSallary formData string true "Employee Salary"
// @Param inputEmployeeType formData string true "Employee Type"
// @Param inputCuti formData string true "Maximum Leave Days"
// @Param name formData string true "Full Name"
// @Param address formData string true "Address"
// @Param email formData string true "Email Address"
// @Param outlet_ids formData string false "Comma-separated list of outlet IDs that the employee has access to"
// @Param phone formData string true "Phone Number"
// @Param jabatanId formData string true "Position/Job Title ID"
// @Param joinDate formData string true "Join Date (YYYY-MM-DD)"
// @Param npwp formData string false "NPWP (Tax ID)"
// @Param inputTitle[] formData []string false "Additional Info Titles"
// @Param info_id[] formData []string false "Additional Info IDs (for updates)"
// @Param withFile[] formData []string false "Has Attachment Flags (true/false)"
// @Param inputInfo[] formData []string false "Additional Info Contents"
// @Param inputAttachment[] formData []file false "Attachments"
// @Param photo formData file false "Employee Photo"
// @Success 200 {object} object "Success response with status"
// @Failure 400 {object} object "Validation error"
// @Failure 500 {object} object "Internal server error"
// @Router /v0/add_employee [post]
func (e *EmployeeHandler) AddEmployee(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		log.Info("error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "wrong form key",
			"error":   err,
		})
	}

	// Define employee form struct
	type EmployeeForm struct {
		TypeID          string `form:"inputTypeId"`
		EmployeeFkid    string `form:"inputEmmployeeName"`
		Nik             string `form:"inputNik"`
		EmployeeSallary int    `form:"inputSallary"`
		TypeFkid        string `form:"inputEmployeeType"`
		MaxLeave        string `form:"inputCuti"`
		// New fields
		Name      string `form:"name"`
		Address   string `form:"address"`
		Phone     string `form:"phone"`
		Email     string `form:"email"`
		JabatanId string `form:"jabatanId"`
		JoinDate  string `form:"joinDate"`
		Npwp      string `form:"npwp"`       // Not required
		OutletIds string `form:"outlet_ids"` // Comma-separated outlet IDs
	}

	//print forms
	// fmt.Println(form.Value)
	log.Info("add employee: %v", cast.ToString(form.Value))

	// Parse employee form data
	employeeForm := new(EmployeeForm)
	if err := c.BodyParser(employeeForm); err != nil {
		fmt.Printf("error parsing employee form: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error parsing employee form",
			"error":   err,
		})
	}

	// Additional employe form
	titles := form.Value["inputTitle[]"]
	ids := form.Value["info_id[]"]
	withfiles := form.Value["withFile[]"]
	infos := form.Value["inputInfo[]"]
	files := form.File["inputAttachment[]"]

	// employee photo profile
	var photo []*multipart.FileHeader
	var profile_img []*multipart.FileHeader
	var fileName string
	if form.File["photo"] != nil {
		photo = form.File["photo"]
		profile_img = form.File["photo"]
		fileName = profile_img[0].Filename

		if photo[0] != nil {
			img_f, err := profile_img[0].Open()
			if err != nil {
				fmt.Printf("open file error: %v", err)
				return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
			}

			f, err := photo[0].Open()
			if err != nil {
				fmt.Printf("opening file error: %v", err)
				return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
			}

			f.Close()

			go e.EmployeeUseCase.GetImageVector(f, img_f, fileName, employeeForm.EmployeeFkid)

		}
	}

	user := domain.GetUserSessionFiber(c)
	// Create employee data struct
	employee := domain.EmployeeData{
		HrmEmployeeID:   employeeForm.TypeID,
		EmployeeFkid:    employeeForm.EmployeeFkid,
		Nik:             employeeForm.Nik,
		EmployeeSallary: employeeForm.EmployeeSallary,
		TypeFkid:        employeeForm.TypeFkid,
		MaxLeave:        employeeForm.MaxLeave,
		Name:            employeeForm.Name,
		Address:         employeeForm.Address,
		Phone:           employeeForm.Phone,
		Email:           employeeForm.Email,
		JabatanId:       employeeForm.JabatanId,
		JoinDate:        employeeForm.JoinDate,
		Npwp:            employeeForm.Npwp,
		OutletIds:       employeeForm.OutletIds,
		AdminId:         user.BusinessId,
	}

	// Use struct validation instead of map validation
	data, err := validate.FromStruct(employee)
	if err != nil {
		fmt.Printf("error validating form: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error validating form",
			"error":   err,
		})
	}

	v := data.Create()

	if v.Validate() {
		v.SafeData()
		// Get url from bucket
		var attach []string
		r := 0
		if len(files) != 0 {
			for _, file := range files {
				f, err := file.Open()
				if err != nil {
					return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
				}
				url, err := bucket.UploadBucket(f, file.Filename, employeeForm.EmployeeFkid, true)
				if err != nil {
					return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
				}
				attach = append(attach, url)
			}
		}

		// Filtering files
		var group []string
		for _, k := range withfiles {
			if k == "true" {
				group = append(group, attach[r])
				r = r + 1
			}
			if k == "false" {
				group = append(group, k)
			}
		}

		// Mapping additional form value
		var data []map[string]any
		if len(titles) == len(infos) {
			for i := range titles {
				data = append(data, map[string]any{
					"add_id":     array.Get(ids, i),
					"title":      array.Get(titles, i),
					"info":       array.Get(infos, i),
					"attachment": array.Get(group, i),
				})
			}
		}

		if employeeForm.TypeID != "" {
			// Update employee with additional info
			if len(titles) != 0 {
				// Updating employee
				_, err = e.EmployeeUseCase.AddEmployee(employee)
				if err != nil {
					fmt.Printf("update info error: %v", err)
					return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
						"message": "error updating info",
						"error":   err,
					})
				}
				var updateInfoData []map[string]interface{}
				var updateInfoDataWithAttc []map[string]interface{}
				var addInfoData []map[string]interface{}
				// filtering info data for update and insert
				for _, v := range data {
					if v["add_id"] != "" {
						if v["attachment"] == "false" {
							delete(v, "attachment")
							updateInfoData = append(updateInfoData, v)
						} else if v["attachment"] != "false" {
							updateInfoDataWithAttc = append(updateInfoDataWithAttc, v)
						}
					}
					if v["add_id"] == "" {
						delete(v, "add_id")
						v["hrm_employee_fkid"] = employeeForm.TypeID
						addInfoData = append(addInfoData, v)
					}
				}
				// update if len updateInfoData not 0
				if len(updateInfoData) != 0 {
					err = e.EmployeeUseCase.UpdateInfo(updateInfoData)
					if err != nil {
						fmt.Printf("update info error: %v", err)
						return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
							"message": "error updating info",
							"error":   err,
						})
					}
				}
				if len(updateInfoDataWithAttc) != 0 {
					err = e.EmployeeUseCase.UpdateInfo(updateInfoDataWithAttc)
					if err != nil {
						fmt.Printf("updating info with attachment error: %v", err)
						return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
							"message": "error updating info with attachment",
							"error":   err,
						})
					}
				}
				if len(addInfoData) != 0 {
					err = e.EmployeeUseCase.AddInfo(addInfoData)
					if err != nil {
						fmt.Printf("error adding info data: %v", err)
						return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
							"message": "error adding info data",
							"error":   err,
						})
					}
				}

				if err != nil {
					return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
						"message": "Failed update data with info",
						"status":  0,
						"error":   err,
					})
				}
				return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
					"message": "Updata data with info success",
					"status":  1,
				})
			}
			// Update employee without additional info
			if len(titles) == 0 {
				fmt.Println("update without info")
				_, err = e.EmployeeUseCase.AddEmployee(employee)
				if err != nil {
					return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
						"message": "Failed update data",
						"status":  0,
						"error":   err,
					})
				}
				return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
					"message": "Updata data success",
					"status":  1,
				})
			}
		}
		if employeeForm.TypeID == "" {
			// Insert new employee
			if len(titles) != 0 {
				// Insert new employee with info
				fmt.Println("insert with info")
				_, err = e.EmployeeUseCase.AddEmployee(employee)
				if err != nil {
					return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
						"message": "Failed add data with info",
						"status":  0,
						"error":   err,
					})
				}
				return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
					"message": "Updata data with info success",
					"status":  1,
				})
			}
			// Insert new employee without info
			if len(titles) == 0 {
				_, err = e.EmployeeUseCase.AddEmployee(employee)
				if err != nil {
					return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
						"message": "Failed add data",
						"status":  0,
						"error":   err,
					})
				}
				return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
					"message": "Add data success",
					"status":  1,
				})
			}
		}
	} else {
		log.Info("add employee validation error: %v", v.Errors)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": v.Errors,
			"error":   "invalid form",
			"status":  0,
		})
	}

	// validation return json
	// var errField map[string]string
	// if v.Errors.HasField("Nik") {
	// 	if v.Errors.Field("Nik")["required"] != "" {
	// 		errField = map[string]string{"inputNik": v.Errors.Field("Nik")["required"]}
	// 	} else if v.Errors.Field("Nik")["minLen"] != "" {
	// 		errField = map[string]string{"inputNik": v.Errors.Field("Nik")["minLen"]}
	// 	} else if v.Errors.Field("Nik")["maxLen"] != "" {
	// 		errField = map[string]string{"inputNik": v.Errors.Field("Nik")["maxLen"]}
	// 	}
	// } else if v.Errors.HasField("MaxLeave") {
	// 	if v.Errors.Field("MaxLeave")["required"] != "" {
	// 		errField = map[string]string{"inputCuti": v.Errors.Field("MaxLeave")["required"]}
	// 	} else if v.Errors.Field("MaxLeave")["minLen"] != "" {
	// 		errField = map[string]string{"inputCuti": v.Errors.Field("MaxLeave")["minLen"]}
	// 	} else if v.Errors.Field("MaxLeave")["maxLen"] != "" {
	// 		errField = map[string]string{"inputCuti": v.Errors.Field("MaxLeave")["maxLen"]}
	// 	}
	// } else if v.Errors.HasField("EmployeeSallary") {
	// 	if v.Errors.Field("EmployeeSallary")["required"] != "" {
	// 		errField = map[string]string{"inputSallary": v.Errors.Field("EmployeeSallary")["required"]}
	// 	} else if v.Errors.Field("EmployeeSallary")["int"] != "" {
	// 		errField = map[string]string{"inputSallary": v.Errors.Field("EmployeeSallary")["int"]}
	// 	}
	// } else if v.Errors.HasField("Name") {
	// 	if v.Errors.Field("Name")["required"] != "" {
	// 		errField = map[string]string{"name": v.Errors.Field("Name")["required"]}
	// 	}
	// } else if v.Errors.HasField("Address") {
	// 	if v.Errors.Field("Address")["required"] != "" {
	// 		errField = map[string]string{"address": v.Errors.Field("Address")["required"]}
	// 	}
	// } else if v.Errors.HasField("Phone") {
	// 	if v.Errors.Field("Phone")["required"] != "" {
	// 		errField = map[string]string{"phone": v.Errors.Field("Phone")["required"]}
	// 	}
	// } else if v.Errors.HasField("JabatanId") {
	// 	if v.Errors.Field("JabatanId")["required"] != "" {
	// 		errField = map[string]string{"jabatanId": v.Errors.Field("JabatanId")["required"]}
	// 	}
	// } else if v.Errors.HasField("JoinDate") {
	// 	if v.Errors.Field("JoinDate")["required"] != "" {
	// 		errField = map[string]string{"joinDate": v.Errors.Field("JoinDate")["required"]}
	// 	} else if v.Errors.Field("JoinDate")["date"] != "" {
	// 		errField = map[string]string{"joinDate": v.Errors.Field("JoinDate")["date"]}
	// 	}
	// }

	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "unknown",
		"status":  0,
	})
}

// GetAddInfo handle func
// @Summary Get additional information for an employee
// @Description Get all additional information records for a specific employee
// @Tags employee
// @Accept json
// @Produce json
// @Param id path int true "Employee ID"
// @Success 200 {array} domain.HrmAddInfo "Additional information records"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/get_info/{id} [get]
func (e *EmployeeHandler) GetAddInfo(c *fiber.Ctx) error {
	info_id := c.Params("id")
	infoID, _ := strconv.Atoi(info_id)
	info, err := e.EmployeeUseCase.GetAddInfo(infoID)
	if err != nil {
		fmt.Printf("error geting add info: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error geting add info",
			"error":   err,
		})
	}
	return c.JSON(info)
}

// DeleteAttach handle func
// @Summary Delete an attachment
// @Description Delete an attachment from additional employee information
// @Tags employee
// @Accept json
// @Produce json
// @Param request body object true "Attachment ID"
// @Param request.id body int true "Attachment ID"
// @Success 200 {object} object "Success response with status"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/delete_attach [post]
func (e *EmployeeHandler) DeleteAttach(c *fiber.Ctx) error {
	type req struct {
		ID int `json:"id"`
	}
	var body req
	c.BodyParser(&body)
	id := map[string]any{
		"add_id": body.ID,
	}

	err := e.EmployeeUseCase.DeleteAttach(id)
	if err != nil {
		fmt.Printf("delete info failed: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": fmt.Sprintf("delete info failed: %v", err),
			"status":  0,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "Delete data success",
		"status":  1,
	})
}

// FetchVectorImg handle func
// @Summary Get employee image vector
// @Description Get the vector representation of an employee's profile image
// @Tags employee
// @Accept json
// @Produce json
// @Param emp_id path int true "Employee ID"
// @Success 200 {array} domain.VectorImg "Image vector data"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/get_vector/{emp_id} [get]
func (e *EmployeeHandler) FetchVectorImg(c *fiber.Ctx) error {
	emp_id := c.Params("emp_id")
	empID, _ := strconv.Atoi(emp_id)
	res, err := e.EmployeeUseCase.FetchVectorImg(empID)
	if err != nil {
		fmt.Printf("fetching image vector error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "fetching image vector error",
			"error":   err,
		})
	}
	return c.JSON(res)
}

// UpdateProfilePhoto handle func
// @Summary Update employee profile photo
// @Description Update an employee's profile photo and generate vector representation
// @Tags employee
// @Accept multipart/form-data
// @Produce json
// @Param emp_id formData string true "Employee ID"
// @Param photo formData file true "Profile photo"
// @Success 200 {object} object "Success response with status"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/update_profile_photo [post]
func (e *EmployeeHandler) UpdateProfilePhoto(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		fmt.Printf("wrong form key: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "wrong form key",
			"error":   err,
		})
	}
	empID := form.Value["emp_id"]
	photo := form.File["photo"]
	f, err := photo[0].Open()
	if err != nil {
		fmt.Printf("opening file error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "opening file error",
			"error":   err,
		})
	}
	f.Close()

	tempFile, err := ioutil.TempFile("temp-images", "upload-*.jpg")
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
	}
	defer tempFile.Close()

	fileBytes, err := ioutil.ReadAll(f)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
	}
	tempFile.Write(fileBytes)
	fileDir, err := os.Getwd()
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
	}
	filePath := path.Join(fileDir, tempFile.Name())
	file, err := os.Open(filePath)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
	}
	defer file.Close()
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	writer.WriteField("emp_id", empID[0])
	part, _ := writer.CreateFormFile("photo", filePath)

	io.Copy(part, file)
	writer.Close()

	resp, _ := http.NewRequest("POST", os.Getenv("API_URL"), body)
	resp.Header.Add("Content-Type", writer.FormDataContentType())
	resp.Body.Close()
	client := &http.Client{}
	response, err := client.Do(resp)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
	}
	var res map[string]interface{}
	json.NewDecoder(response.Body).Decode(&res)
	response.Body.Close()
	// id, _ := strconv.Atoi(empID[0])
	// err = e.EmployeeUseCase.UpdatePofilePhoto(id, res["img_vector"])
	if err != nil {
		fmt.Printf("updatign vector error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "updating vector error",
			"error":   err,
		})
	}
	f.Close()
	tempFile.Close()
	file.Close()
	err = os.Remove(filePath)
	if err != nil {
		fmt.Printf("removing file error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "removing file error",
			"error":   err,
			"status":  fiber.ErrInternalServerError.Code,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "update image profile success",
		"error":   err,
		"status":  c.Response().StatusCode(),
	})
}

// ChangePassword handle func
// @Summary Change employee password
// @Description Change an employee's password with verification of old password
// @Tags employee
// @Accept multipart/form-data
// @Produce json
// @Param email formData string true "Employee email"
// @Param old_pass formData string true "Old password"
// @Param new_pass formData string true "New password"
// @Success 200 {object} object "Success response with status"
// @Failure 400 {object} object "Bad request or validation error"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/change_password [post]
func (e *EmployeeHandler) ChangePassword(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		log.IfError(err)
		return c.Status(fiber.ErrBadRequest.Code).JSON(&fiber.Map{
			"message": "wrong form key",
			"status":  fiber.ErrBadRequest.Code,
		})
	}
	email := form.Value["email"][0]
	oldPass := form.Value["old_pass"][0]
	newPass := form.Value["new_pass"][0]
	nPass, err := bcrypt.GenerateFromPassword([]byte(newPass), bcrypt.MinCost)
	if err != nil {
		log.IfError(err)
	}
	res, err := e.EmployeeUseCase.FetchEmailNPassword(email)
	if err != nil {
		log.IfError(err)
		return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
			"message": "fetching email and password error",
			"status":  c.Response().StatusCode(),
		})
	}
	if res.Email == "" {
		fmt.Printf("email not found: %v \n", email)
		return c.Status(fiber.ErrBadRequest.Code).JSON(&fiber.Map{
			"message": "user email not found",
			"status":  fiber.ErrBadRequest.Code,
		})
	}
	err = bcrypt.CompareHashAndPassword([]byte(res.Password), []byte(oldPass))
	fmt.Println(res.Password)
	fmt.Println([]byte(oldPass))
	if err != nil {
		fmt.Printf("compare hash password error: %v", err)
		log.IfError(err)
		return c.Status(fiber.ErrBadRequest.Code).JSON(&fiber.Map{
			"message": "password not match",
			"status":  fiber.ErrBadRequest.Code,
		})
	}
	err = e.EmployeeUseCase.ChangePassword(email, oldPass, string(nPass))
	if err != nil {
		fmt.Printf("employee handler change password error: %v", err)
		log.IfError(err)
		return c.Status(fiber.ErrBadRequest.Code).JSON(&fiber.Map{
			"message": "change password failed",
			"status":  fiber.ErrBadRequest.Code,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "change password success",
		"status":  c.Response().StatusCode(),
	})

}

// GetEmployeeV2 handle func
// @Summary Get detailed employee data with flattened additional info
// @Description Get detailed employee data including job position, employee type and flattened additional information. Supports filtering by outlet IDs, employee type IDs, and jabatan IDs.
// @Tags employee
// @Accept json
// @Produce json
// @Param outlet_ids query string false "Comma-separated outlet IDs for filtering (e.g., 45,67,4)"
// @Param employee_type_ids query string false "Comma-separated employee type IDs for filtering (e.g., 1,2,3)"
// @Param jabatan_ids query string false "Comma-separated jabatan IDs for filtering (e.g., 1,2,3)"
// @Success 200 {array} object "List of employees with flattened additional info"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/employee [get]
func (e *EmployeeHandler) GetEmployeeV2(c *fiber.Ctx) error {
	// businessId := c.Get("business_id")
	// businessID, _ := strconv.Atoi(businessId)

	user := domain.GetUserSessionFiber(c)
	businessID := user.BusinessId

	// Parse filter parameters
	var filter domain.EmployeeFilter

	// Parse filter parameters using helper function
	filter.OutletIDs = parseCommaSeparatedIDs(c.Query("outlet_ids"))
	filter.EmployeeTypeIDs = parseCommaSeparatedIDs(c.Query("employee_type_ids"))
	filter.JabatanIDs = parseCommaSeparatedIDs(c.Query("jabatan_ids"))

	// Use the unified GetEmployeeV2 method with optional filter
	employees, err := e.EmployeeUseCase.GetEmployeeV2(businessID, filter)

	if err != nil {
		log.IfError(err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching employee data",
			"error":   err,
		})
	}
	return c.JSON(employees)
}
